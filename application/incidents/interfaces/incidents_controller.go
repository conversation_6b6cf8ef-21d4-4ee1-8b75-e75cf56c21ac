package incidentsinterfaces

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/entities"
	incidentsUsecases "git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/incidents/usecases"
	"github.com/labstack/echo/v4"
)

type IncidentsController struct {
	usecase *incidentsUsecases.IncidentsUsecase
}

func NewIncidentsController(u *incidentsUsecases.IncidentsUsecase) *IncidentsController {
	return &IncidentsController{usecase: u}
}

func (c *IncidentsController) List(ctx echo.Context) error {
	// クエリパラメータの取得
	keyword := ctx.QueryParam("keyword")
	startDateStr := ctx.QueryParam("startDate")
	endDateStr := ctx.QueryParam("endDate")

	var startDate *time.Time
	if startDateStr != "" {
		parsedDate, err := time.Parse("2006-01-02", startDateStr)
		if err != nil {
			jsonErr := ctx.JSON(http.StatusBadRequest, entities.ErrorResponse{
				Status:  http.StatusBadRequest,
				Message: "Bad Request",
				Error: entities.BadRequestError{
					ErrorCode: "400",
					Message:   "invalid startDate format. Use YYYY-MM-DD.",
				},
				Body: map[string]interface{}{},
			})

			return fmt.Errorf("failed to write BadRequest JSON for invalid startDate: %w", jsonErr)
		}
		startDate = &parsedDate
	}

	var endDate *time.Time
	if endDateStr != "" {
		parsedDate, err := time.Parse("2006-01-02", endDateStr)
		if err != nil {
			jsonErr := ctx.JSON(http.StatusBadRequest, entities.ErrorResponse{
				Status:  http.StatusBadRequest,
				Message: "Bad Request",
				Error: entities.BadRequestError{
					ErrorCode: "400",
					Message:   "invalid endDate format. Use YYYY-MM-DD.",
				},
				Body: map[string]interface{}{},
			})

			return fmt.Errorf("failed to write BadRequest JSON for invalid endDate: %w", jsonErr)
		}
		endDate = &parsedDate
	}

	items, err := c.usecase.List(ctx.Request().Context(), keyword, startDate, endDate)
	if err != nil {
		jsonErr := ctx.JSON(http.StatusInternalServerError, entities.ErrorResponse{
			Status:  http.StatusInternalServerError,
			Message: "Internal Server Error",
			Error: entities.InternalServerError{
				ErrorCode: "500",
				Message:   err.Error(),
			},
			Body: map[string]interface{}{},
		})

		return fmt.Errorf("failed to write InternalServerError JSON in List: %w", jsonErr)
	}

	if err := ctx.JSON(http.StatusOK, entities.SuccessResponse{
		Status:  http.StatusOK,
		Message: "OK",
		Error:   map[string]interface{}{},
		Body: map[string]interface{}{
			"incidents": items,
		},
	}); err != nil {
		return fmt.Errorf("failed to write OK JSON in List: %w", err)
	}

	return nil
}

func (c *IncidentsController) Detail(ctx echo.Context) error {
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		jsonErr := ctx.JSON(http.StatusBadRequest, entities.ErrorResponse{
			Status:  http.StatusBadRequest,
			Message: "Bad Request",
			Error: entities.BadRequestError{
				ErrorCode: "400",
				Message:   "invalid id",
			},
			Body: map[string]interface{}{},
		})

		return fmt.Errorf("failed to write BadRequest JSON for invalid id: %w", jsonErr)
	}
	item, err := c.usecase.GetByID(ctx.Request().Context(), id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			jsonErr := ctx.JSON(http.StatusNotFound, entities.ErrorResponse{
				Status:  http.StatusNotFound,
				Message: "Not Found",
				Error: entities.NotFoundError{
					ErrorCode: "404",
					Message:   "incidents not found",
				},
				Body: map[string]interface{}{},
			})

			return fmt.Errorf("failed to write NotFound JSON in Detail: %w", jsonErr)
		}

		jsonErr := ctx.JSON(http.StatusInternalServerError, entities.ErrorResponse{
			Status:  http.StatusInternalServerError,
			Message: "Internal Server Error",
			Error: entities.InternalServerError{
				ErrorCode: "500",
				Message:   err.Error(),
			},
			Body: map[string]interface{}{},
		})

		return fmt.Errorf("failed to write InternalServerError JSON in Detail: %w", jsonErr)
	}

	if err := ctx.JSON(http.StatusOK, entities.SuccessResponse{
		Status:  http.StatusOK,
		Message: "OK",
		Error:   map[string]interface{}{},
		Body:    item,
	}); err != nil {
		return fmt.Errorf("failed to write OK JSON in Detail: %w", err)
	}

	return nil
}
